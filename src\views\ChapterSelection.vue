<template>
  <div>
    <!-- 頂部導航欄 -->
    <TopNavigation />

    <div class="exam-system-container">
      <div class="exam-card large">
        <!-- 返回按鈕 -->
        <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
          返回
        </button>

        <div class="text-center mb-4">
          <h1 class="display-4 gradient-text fw-bold mb-3">選擇章節</h1>
          <p class="text-muted fs-5">{{ languageTitle }}</p>
        </div>

        <!-- 顯示 Realtime Database 資訊 -->
        <div v-if="databaseInfo" class="alert alert-info mb-4">
          <h5><i class="fas fa-database me-2"></i>Realtime Database "中文" 資料</h5>
          <p><strong>章節數量:</strong> {{ databaseInfo.chapterCount }}</p>
          <p><strong>章節列表:</strong> {{ databaseInfo.chapterNames.join(', ') }}</p>
          <p><strong>總題目數:</strong> {{ databaseInfo.totalQuestions }}</p>
          <details>
            <summary class="btn btn-sm btn-outline-info">查看完整資料結構</summary>
            <pre class="mt-2 p-2 bg-light border rounded"
              style="max-height: 300px; overflow-y: auto;">{{ databaseInfo.rawData }}</pre>
          </details>
        </div>

        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
          </div>
          <p class="mt-3 text-muted">載入章節資料中...</p>
        </div>

        <div v-else-if="error" class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i>
          {{ error }}
        </div>

        <div v-else class="row g-3 mb-4">
          <div v-for="chapter in chapters" :key="chapter.id" class="col-lg-4 col-md-6">
            <div class="chapter-card h-100" @click="startExam(chapter)">
              <div class="text-center">
                <div class="fs-1 fw-bold text-primary mb-3">{{ chapter.id }}</div>
                <h5 class="fw-bold mb-2">{{ chapter.title }}</h5>
                <p class="text-muted small mb-3">{{ chapter.desc }}</p>
                <div class="badge bg-primary">
                  <i class="fas fa-question-circle me-1"></i>
                  點擊開始測驗
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import TopNavigation from '../components/TopNavigation.vue'
import { ref as dbRef, get } from 'firebase/database'
import { database } from '../firebase/config'

const router = useRouter()
const examStore = useExamStore()

const chapters = ref([])
const loading = ref(true)
const error = ref('')
const databaseInfo = ref(null)

// 檢查登入狀態和語言選擇
if (!examStore.user.isLoggedIn) {
  router.push('/')
} else if (!examStore.currentLanguage) {
  router.push('/language')
}

const languageTitle = computed(() => {
  return examStore.currentLanguage === '中文' ? '中文測驗章節' : 'English Test Chapters'
})

// 印出 Realtime Database 中"中文"物件的內容
const printChineseData = async () => {
  try {
    console.log('=== 開始獲取 Realtime Database 中的"中文"資料 ===')
    const chineseRef = dbRef(database, '中文')
    const snapshot = await get(chineseRef)

    if (snapshot.exists()) {
      const chineseData = snapshot.val()
      console.log('=== Realtime Database "中文" 物件內容 ===')
      console.log(JSON.stringify(chineseData, null, 2))

      // 印出章節名稱
      const chapterNames = Object.keys(chineseData)
      console.log('=== 章節列表 ===')

      let totalQuestions = 0
      chapterNames.forEach((chapterName, index) => {
        const questionCount = chineseData[chapterName].length
        console.log(`${index + 1}. ${chapterName}`)
        console.log(`   題目數量: ${questionCount}`)
        totalQuestions += questionCount
      })

      // 設置頁面顯示的資料庫資訊
      databaseInfo.value = {
        chapterCount: chapterNames.length,
        chapterNames: chapterNames,
        totalQuestions: totalQuestions,
        rawData: JSON.stringify(chineseData, null, 2)
      }

      // 印出第一個章節的詳細內容作為範例
      if (chapterNames.length > 0) {
        const firstChapter = chapterNames[0]
        console.log(`=== "${firstChapter}" 詳細內容 ===`)
        console.log(JSON.stringify(chineseData[firstChapter], null, 2))
      }

      console.log(`=== 總計: ${chapterNames.length} 個章節，${totalQuestions} 道題目 ===`)
    } else {
      console.log('=== Realtime Database 中沒有"中文"資料 ===')
      databaseInfo.value = {
        chapterCount: 0,
        chapterNames: [],
        totalQuestions: 0,
        rawData: '沒有資料'
      }
    }
  } catch (error) {
    console.error('=== 獲取"中文"資料時發生錯誤 ===', error)
    databaseInfo.value = {
      chapterCount: 0,
      chapterNames: [],
      totalQuestions: 0,
      rawData: `錯誤: ${error.message}`
    }
  }
}

// 載入章節資料
const loadChapters = async () => {
  loading.value = true
  error.value = ''

  // 印出 Realtime Database 內容
  await printChineseData()

  try {
    // 直接從 Firebase 載入章節資料
    const chineseRef = dbRef(database, examStore.currentLanguage)
    const snapshot = await get(chineseRef)

    if (snapshot.exists()) {
      const data = snapshot.val()
      console.log('載入的章節資料:', data)

      // 轉換資料格式：第一層 key 是章節名稱，value 是題目陣列
      const chapterList = Object.keys(data).map((chapterName, index) => ({
        id: index + 1,
        title: chapterName, // 直接使用第一層的 key 作為標題
        name: chapterName,  // 保存原始名稱用於後續查詢
        desc: `${chapterName}相關題目`,
        questionCount: data[chapterName].length
      }))

      console.log('轉換後的章節列表:', chapterList)
      chapters.value = chapterList
    } else {
      error.value = '找不到章節資料'
      chapters.value = []
    }
  } catch (err) {
    error.value = '載入章節資料失敗，請重新整理頁面'
    console.error('載入章節錯誤:', err)
    chapters.value = []
  } finally {
    loading.value = false
  }
}

const startExam = async (chapter) => {
  const success = await examStore.startExam(chapter)
  if (success) {
    router.push(`/exam/${chapter.id}`)
  } else {
    error.value = '載入考試題目失敗，請重試'
  }
}

const goBack = () => {
  router.push('/language')
}

onMounted(() => {
  loadChapters()
})
</script>

<style scoped>
@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .chapter-card {
    padding: 20px;
  }
}
</style>
