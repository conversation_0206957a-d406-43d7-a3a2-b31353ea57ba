<template>
  <div>
    <!-- 頂部導航欄 -->
    <TopNavigation />

    <div class="exam-system-container">
      <div class="exam-card large">
        <!-- 返回按鈕 -->
        <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
          返回
        </button>

        <div class="text-center mb-4">
          <h1 class="display-4 gradient-text fw-bold mb-3">選擇章節</h1>
          <p class="text-muted fs-5">{{ languageTitle }}</p>
        </div>

        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
          </div>
          <p class="mt-3 text-muted">載入章節資料中...</p>
        </div>

        <div v-else-if="error" class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i>
          {{ error }}
        </div>

        <div v-else class="row g-3 mb-4">
          <div v-for="chapter in chapters" :key="chapter.id" class="col-lg-4 col-md-6">
            <div class="chapter-card h-100" @click="startExam(chapter)">
              <div class="text-center">
                <div class="fs-1 fw-bold text-primary mb-3">{{ chapter.id }}</div>
                <h5 class="fw-bold mb-2">{{ chapter.title }}</h5>
                <p class="text-muted small mb-3">{{ chapter.desc }}</p>
                <div class="badge bg-primary">
                  <i class="fas fa-question-circle me-1"></i>
                  點擊開始測驗
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import TopNavigation from '../components/TopNavigation.vue'

const router = useRouter()
const examStore = useExamStore()

const chapters = ref([])
const loading = ref(true)
const error = ref('')

// 檢查登入狀態和語言選擇
if (!examStore.user.isLoggedIn) {
  router.push('/')
} else if (!examStore.currentLanguage) {
  router.push('/language')
}

const languageTitle = computed(() => {
  return examStore.currentLanguage === '中文' ? '中文測驗章節' : 'English Test Chapters'
})

// 載入章節資料
const loadChapters = async () => {
  loading.value = true
  error.value = ''

  try {
    const result = await examStore.loadChapters(examStore.currentLanguage)
    chapters.value = result
  } catch (err) {
    error.value = '載入章節資料失敗，請重新整理頁面'
    console.error('載入章節錯誤:', err)
  } finally {
    loading.value = false
  }
}

const startExam = async (chapter) => {
  const success = await examStore.startExam(chapter)
  if (success) {
    router.push(`/exam/${chapter.id}`)
  } else {
    error.value = '載入考試題目失敗，請重試'
  }
}

const goBack = () => {
  router.push('/language')
}

onMounted(() => {
  loadChapters()
})
</script>

<style scoped>
@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .chapter-card {
    padding: 20px;
  }
}
</style>
