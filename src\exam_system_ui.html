<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考試練習系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 800px;
            padding: 40px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .page {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        
        .page.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .language-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .language-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .language-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(240, 147, 251, 0.4);
        }
        
        .language-card.english {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        
        .chapters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chapter-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chapter-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }
        
        .chapter-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .chapter-title {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 10px;
        }
        
        .chapter-desc {
            font-size: 0.9rem;
            color: #666;
        }
        
        .question-counter {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.1rem;
            color: #666;
        }
        
        .question-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .question-text {
            font-size: 1.2rem;
            margin-bottom: 25px;
            color: #333;
            line-height: 1.6;
        }
        
        .options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .option {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option.correct {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }
        
        .option.incorrect {
            border-color: #dc3545;
            background: #dc3545;
            color: white;
        }
        
        .option-letter {
            font-weight: bold;
            margin-right: 15px;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: #e1e5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }
        
        .option.selected .option-letter,
        .option.correct .option-letter,
        .option.incorrect .option-letter {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        
        .result-card {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .score {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .score-text {
            font-size: 1.3rem;
            margin-bottom: 20px;
        }
        
        .performance {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .review-section {
            margin-top: 30px;
        }
        
        .review-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .back-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #f0f4ff;
            border: 2px solid #667eea;
            font-size: 1.2rem;
            cursor: pointer;
            color: #667eea;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            z-index: 1000;
            font-weight: bold;
        }
        
        .back-btn:hover {
            background: #f0f4ff;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登入頁面 -->
        <div class="page active" id="loginPage">
            <div class="header">
                <h1 class="logo">📚 考試練習系統</h1>
                <p class="subtitle">提升您的學習成效</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">使用者名稱</label>
                <input type="text" class="form-input" id="username" placeholder="請輸入使用者名稱">
            </div>
            
            <div class="form-group">
                <label class="form-label">密碼</label>
                <input type="password" class="form-input" id="password" placeholder="請輸入密碼">
            </div>
            
            <button class="btn" onclick="login()">登入系統</button>
        </div>
        
        <!-- 首頁 - 語言選擇 -->
        <div class="page" id="homePage">
            <button class="back-btn" onclick="logout()">⬅</button>
            <div class="header">
                <h1 class="logo">選擇測驗語言</h1>
                <p class="subtitle">請選擇您要練習的語言</p>
            </div>
            
            <div class="language-selector">
                <button class="language-card" onclick="selectLanguage('chinese')">
                    <div style="font-size: 3rem; margin-bottom: 15px;">🇹🇼</div>
                    <div>中文測驗</div>
                    <div style="font-size: 0.9rem; margin-top: 5px; opacity: 0.8;">Traditional Chinese</div>
                </button>
                
                <button class="language-card english" onclick="selectLanguage('english')">
                    <div style="font-size: 3rem; margin-bottom: 15px;">🇺🇸</div>
                    <div>English Test</div>
                    <div style="font-size: 0.9rem; margin-top: 5px; opacity: 0.7;">英文測驗</div>
                </button>
            </div>
        </div>
        
        <!-- 章節選擇頁面 -->
        <div class="page" id="chaptersPage">
            <button class="back-btn" onclick="goToHome()">⬅</button>
            <div class="header">
                <h1 class="logo">選擇章節</h1>
                <p class="subtitle" id="languageTitle">中文測驗章節</p>
            </div>
            
            <div class="chapters-grid" id="chaptersGrid">
                <!-- 章節卡片將由JavaScript動態生成 -->
            </div>
        </div>
        
        <!-- 考試頁面 -->
        <div class="page" id="examPage">
            <button class="back-btn" onclick="goToChapters()">⬅</button>
            <div class="question-counter" id="questionCounter">第 1 題 / 共 5 題</div>
            
            <div class="question-card">
                <div class="question-text" id="questionText">題目載入中...</div>
                <div class="options" id="optionsContainer">
                    <!-- 選項將由JavaScript動態生成 -->
                </div>
            </div>
            
            <div class="nav-buttons">
                <button class="btn-secondary" id="prevBtn" onclick="prevQuestion()" style="display: none;">上一題</button>
                <button class="btn" id="nextBtn" onclick="nextQuestion()">下一題</button>
            </div>
        </div>
        
        <!-- 結果頁面 -->
        <div class="page" id="resultPage">
            <div class="result-card">
                <div class="score" id="finalScore">85</div>
                <div class="score-text">您的成績</div>
                <div class="performance" id="performance">表現優異！繼續保持！</div>
            </div>
            
            <div class="review-section">
                <h3 class="review-title">🔍 答題回顧</h3>
                <div id="reviewContainer">
                    <!-- 錯誤題目回顧將由JavaScript生成 -->
                </div>
            </div>
            
            <button class="btn" onclick="goToChapters()" style="margin-top: 30px;">返回章節選擇</button>
        </div>
    </div>

    <script>
        // 全域變數
        let currentLanguage = '';
        let currentChapter = '';
        let currentQuestionIndex = 0;
        let userAnswers = [];
        let questions = [];
        
        // 題目資料
        const questionData = {
            chinese: {
                chapters: [
                    {id: 1, title: '基礎語法', desc: '基本語法概念與應用', questions: [
                        {q: '下列哪個是正確的句子？', options: ['他很高興。', '他很高興的。', '他很高興了。', '他很高興呢。'], answer: 0},
                        {q: '「的」和「得」的用法，下列何者正確？', options: ['他跑得很快', '他跑的很快', '美麗的花', '美麗得花'], answer: 0},
                        {q: '下列哪個詞語是動詞？', options: ['美麗', '跑步', '高興', '桌子'], answer: 1},
                        {q: '「因為...所以...」的用法，下列何者正確？', options: ['因為下雨，所以我帶傘', '因為下雨，我帶傘', '下雨，所以我帶傘', '因為下雨所以我帶傘'], answer: 0},
                        {q: '下列哪個是疑問句？', options: ['今天天氣很好。', '今天天氣好嗎？', '今天天氣真好！', '今天天氣好。'], answer: 1}
                    ]},
                    {id: 2, title: '詞彙應用', desc: '常用詞彙的正確使用', questions: [
                        {q: '「學習」的同義詞是？', options: ['教導', '研讀', '遊戲', '休息'], answer: 1},
                        {q: '下列哪個詞語表示「非常」的意思？', options: ['有點', '稍微', '極其', '還算'], answer: 2},
                        {q: '「高興」的反義詞是？', options: ['快樂', '難過', '興奮', '滿足'], answer: 1},
                        {q: '下列哪個詞語可以形容天氣？', options: ['聰明', '晴朗', '勤奮', '友善'], answer: 1},
                        {q: '「朋友」的量詞是？', options: ['一個朋友', '一位朋友', '一名朋友', '以上皆可'], answer: 3}
                    ]},
                    {id: 3, title: '閱讀理解', desc: '短文閱讀與理解能力', questions: [
                        {q: '小明每天早上七點起床，八點上學。請問小明幾點起床？', options: ['六點', '七點', '八點', '九點'], answer: 1},
                        {q: '「春天來了，花兒開了，鳥兒唱歌了。」這句話描述的是什麼季節？', options: ['春天', '夏天', '秋天', '冬天'], answer: 0},
                        {q: '根據文章「媽媽買了蘋果、香蕉和橘子」，媽媽買了幾種水果？', options: ['一種', '兩種', '三種', '四種'], answer: 2},
                        {q: '「圖書館很安靜，大家都在認真讀書。」這裡的「安靜」是什麼意思？', options: ['吵鬧', '沒有聲音', '很熱鬧', '很忙碌'], answer: 1},
                        {q: '「他走得很慢，但是很穩。」這句話說明了什麼？', options: ['他走得快', '他走得慢而穩', '他不會走路', '他在跑步'], answer: 1}
                    ]},
                    {id: 4, title: '寫作基礎', desc: '基本寫作技巧與結構', questions: [
                        {q: '一篇好的文章應該包含什麼？', options: ['只有開頭', '開頭、內容、結尾', '只有內容', '只有結尾'], answer: 1},
                        {q: '寫作時，段落之間應該如何連接？', options: ['直接連接', '用轉折詞', '不用連接', '隨意連接'], answer: 1},
                        {q: '下列哪個是好的開頭方式？', options: ['直接結論', '提出問題', '列舉事實', '以上皆可'], answer: 3},
                        {q: '寫作時應該注意什麼？', options: ['只注意內容', '只注意格式', '內容和格式都重要', '都不重要'], answer: 2},
                        {q: '修改文章時，首先應該檢查什麼？', options: ['錯字', '標點符號', '內容邏輯', '字數'], answer: 2}
                    ]},
                    {id: 5, title: '文學常識', desc: '基本文學知識與作品', questions: [
                        {q: '下列哪位是著名的中國古代詩人？', options: ['李白', '牛頓', '愛因斯坦', '莎士比亞'], answer: 0},
                        {q: '「靜夜思」是誰的作品？', options: ['杜甫', '李白', '白居易', '王維'], answer: 1},
                        {q: '中國古代四大名著不包括哪一部？', options: ['西遊記', '水滸傳', '三國演義', '聊齋誌異'], answer: 3},
                        {q: '「詩聖」指的是哪位詩人？', options: ['李白', '杜甫', '白居易', '王維'], answer: 1},
                        {q: '下列哪個是中國傳統節日？', options: ['聖誕節', '中秋節', '萬聖節', '復活節'], answer: 1}
                    ]},
                    {id: 6, title: '綜合測驗', desc: '綜合各項語文能力', questions: [
                        {q: '「書中自有黃金屋」這句話的意思是？', options: ['書裡有金子', '讀書能獲得知識財富', '書很貴重', '書是黃金做的'], answer: 1},
                        {q: '下列哪個句子使用了比喻的修辭技巧？', options: ['他很高', '他像山一樣高', '他比我高', '他最高'], answer: 1},
                        {q: '「一寸光陰一寸金」告訴我們什麼道理？', options: ['金子很珍貴', '時間很珍貴', '光陰似箭', '寸金難買'], answer: 1},
                        {q: '下列哪個是正確的標點符號使用？', options: ['你好嗎。', '你好嗎？', '你好嗎！', '你好嗎，'], answer: 1},
                        {q: '「學而時習之，不亦說乎？」出自哪本書？', options: ['論語', '孟子', '老子', '莊子'], answer: 0}
                    ]}
                ]
            },
            english: {
                chapters: [
                    {id: 1, title: 'Basic Grammar', desc: 'Fundamental grammar concepts', questions: [
                        {q: 'Which sentence is correct?', options: ['He is very happy.', 'He very happy.', 'He is very happily.', 'He very happily.'], answer: 0},
                        {q: 'Choose the correct form of "be":', options: ['I are student', 'I am student', 'I am a student', 'I are a student'], answer: 2},
                        {q: 'What is the past tense of "go"?', options: ['goed', 'went', 'gone', 'going'], answer: 1},
                        {q: 'Which is a verb?', options: ['beautiful', 'run', 'happiness', 'table'], answer: 1},
                        {q: 'Choose the correct question:', options: ['What your name?', 'What is your name?', 'What your name is?', 'Your name what is?'], answer: 1}
                    ]},
                    {id: 2, title: 'Vocabulary', desc: 'Common vocabulary usage', questions: [
                        {q: 'What is a synonym for "happy"?', options: ['sad', 'angry', 'joyful', 'tired'], answer: 2},
                        {q: 'Which word means "very big"?', options: ['tiny', 'huge', 'small', 'little'], answer: 1},
                        {q: 'What is the opposite of "hot"?', options: ['warm', 'cool', 'cold', 'freezing'], answer: 2},
                        {q: 'Which word describes weather?', options: ['intelligent', 'sunny', 'friendly', 'delicious'], answer: 1},
                        {q: 'What do you call a person who teaches?', options: ['student', 'teacher', 'doctor', 'driver'], answer: 1}
                    ]},
                    {id: 3, title: 'Reading', desc: 'Reading comprehension skills', questions: [
                        {q: 'Tom wakes up at 7 AM and goes to school at 8 AM. What time does Tom wake up?', options: ['6 AM', '7 AM', '8 AM', '9 AM'], answer: 1},
                        {q: '"Spring is here, flowers bloom, birds sing." What season is described?', options: ['Spring', 'Summer', 'Fall', 'Winter'], answer: 0},
                        {q: 'According to "Mom bought apples, bananas, and oranges", how many types of fruit did Mom buy?', options: ['one', 'two', 'three', 'four'], answer: 2},
                        {q: '"The library is quiet, everyone is reading seriously." What does "quiet" mean?', options: ['noisy', 'silent', 'crowded', 'busy'], answer: 1},
                        {q: '"He walks slowly but steadily." What does this sentence tell us?', options: ['He walks fast', 'He walks slowly but steadily', 'He cannot walk', 'He is running'], answer: 1}
                    ]},
                    {id: 4, title: 'Writing', desc: 'Basic writing skills', questions: [
                        {q: 'What should a good essay include?', options: ['Only introduction', 'Introduction, body, conclusion', 'Only body', 'Only conclusion'], answer: 1},
                        {q: 'How should paragraphs be connected in writing?', options: ['Direct connection', 'Using transition words', 'No connection needed', 'Random connection'], answer: 1},
                        {q: 'Which is a good way to start an essay?', options: ['Direct conclusion', 'Ask a question', 'State facts', 'All of the above'], answer: 3},
                        {q: 'What should you pay attention to when writing?', options: ['Only content', 'Only format', 'Both content and format', 'Neither'], answer: 2},
                        {q: 'When revising an essay, what should you check first?', options: ['Spelling', 'Punctuation', 'Logic of content', 'Word count'], answer: 2}
                    ]},
                    {id: 5, title: 'Literature', desc: 'Basic literary knowledge', questions: [
                        {q: 'Who wrote "Romeo and Juliet"?', options: ['Charles Dickens', 'William Shakespeare', 'Jane Austen', 'Mark Twain'], answer: 1},
                        {q: 'What is a haiku?', options: ['A novel', 'A short poem', 'A play', 'A song'], answer: 1},
                        {q: 'Which is NOT a genre of literature?', options: ['Poetry', 'Fiction', 'Drama', 'Mathematics'], answer: 3},
                        {q: 'What is the main character in a story called?', options: ['Antagonist', 'Protagonist', 'Narrator', 'Author'], answer: 1},
                        {q: 'What is a metaphor?', options: ['A direct comparison', 'An indirect comparison', 'A question', 'A statement'], answer: 1}
                    ]},
                    {id: 6, title: 'Comprehensive', desc: 'Mixed language skills test', questions: [
                        {q: 'Which sentence uses correct punctuation?', options: ['Hello, how are you.', 'Hello, how are you?', 'Hello how are you?', 'Hello, how are you!'], answer: 1},
                        {q: 'What figure of speech is "Life is a journey"?', options: ['Simile', 'Metaphor', 'Alliteration', 'Onomatopoeia'], answer: 1},
                        {q: 'Which is a complete sentence?', options: ['Running fast', 'The dog', 'She runs fast', 'Very quickly'], answer: 2},
                        {q: 'What is the correct order for an essay?', options: ['Conclusion, body, introduction', 'Body, introduction, conclusion', 'Introduction, body, conclusion', 'Introduction, conclusion, body'], answer: 2},
                        {q: 'Which shows correct subject-verb agreement?', options: ['The dogs runs', 'The dog run', 'The dogs run', 'The dog are running'], answer: 2}
                    ]}
                ]
            }
        };
        
        // 登入功能
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username && password) {
                showPage('homePage');
            } else {
                alert('請輸入使用者名稱和密碼');
            }
        }
        
        // 登出功能
        function logout() {
            showPage('loginPage');
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
        }
        
        // 選擇語言
        function selectLanguage(language) {
            currentLanguage = language;
            loadChapters();
            showPage('chaptersPage');
        }
        
        // 載入章節
        function loadChapters() {
            const chaptersGrid = document.getElementById('chaptersGrid');
            const languageTitle = document.getElementById('languageTitle');
            const chapters = questionData[currentLanguage].chapters;
            
            languageTitle.textContent = currentLanguage === 'chinese' ? '中文測驗章節' : 'English Test Chapters';
            
            chaptersGrid.innerHTML = '';
            chapters.forEach(chapter => {
                const chapterCard = document.createElement('div');
                chapterCard.className = 'chapter-card';
                chapterCard.onclick = () => startExam(chapter.id);
                chapterCard.innerHTML = `
                    <div class="chapter-number">${chapter.id}</div>
                    <div class="chapter-title">${chapter.title}</div>
                    <div class="chapter-desc">${chapter.desc}</div>
                `;
                chaptersGrid.appendChild(chapterCard);
            });
        }
        
        // 開始考試
        function startExam(chapterId) {
            currentChapter = chapterId;
            questions = questionData[currentLanguage].chapters.find(ch => ch.id === chapterId).questions;
            currentQuestionIndex = 0;
            userAnswers = new Array(questions.length).fill(-1);
            
            loadQuestion();
            showPage('examPage');
        }
        
        // 載入題目
        function loadQuestion() {
            const question = questions[currentQuestionIndex];
            const questionCounter = document.getElementById('questionCounter');
            const questionText = document.getElementById('questionText');
            const optionsContainer = document.getElementById('optionsContainer');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            questionCounter.textContent = `第 ${currentQuestionIndex + 1} 題 / 共 ${questions.length} 題`;
            questionText.textContent = question.q;
            
            optionsContainer.innerHTML = '';
            question.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                if (userAnswers[currentQuestionIndex] === index) {
                    optionDiv.classList.add('selected');
                }
                optionDiv.onclick = () => selectOption(index);
                optionDiv.innerHTML = `
                    <div class="option-letter">${String.fromCharCode(65 + index)}</div>
                    <div>${option}</div>
                `;
                optionsContainer.appendChild(optionDiv);
            });
            
            prevBtn.style.display = currentQuestionIndex > 0 ? 'block' : 'none';
            nextBtn.textContent = currentQuestionIndex === questions.length - 1 ? '提交答案' : '下一題';
        }
        
        // 選擇答案
        function selectOption(index) {
            userAnswers[currentQuestionIndex] = index;
            
            const options = document.querySelectorAll('.option');
            options.forEach((option, i) => {
                option.classList.toggle('selected', i === index);
            });
        }
        
        // 下一題
        function nextQuestion() {
            if (currentQuestionIndex === questions.length - 1) {
                // 提交答案
                showResults();
            } else {
                currentQuestionIndex++;
                loadQuestion();
            }
        }
        
        // 上一題
        function prevQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                loadQuestion();
            }
        }
        
        // 顯示結果
        function showResults() {
            let correctCount = 0;
            const incorrectQuestions = [];
            
            userAnswers.forEach((answer, index) => {
                if (answer === questions[index].answer) {
                    correctCount++;
                } else {
                    incorrectQuestions.push({
                        questionIndex: index,
                        question: questions[index],
                        userAnswer: answer,
                        correctAnswer: questions[index].answer
                    });
                }
            });
            
            const score = Math.round((correctCount / questions.length) * 100);
            const finalScoreElement = document.getElementById('finalScore');
            const performanceElement = document.getElementById('performance');
            const reviewContainer = document.getElementById('reviewContainer');
            
            finalScoreElement.textContent = score;
            
            if (score >= 90) {
                performanceElement.textContent = '🎉 優秀！您的表現非常出色！';
            } else if (score >= 80) {
                performanceElement.textContent = '👍 良好！繼續保持努力！';
            } else if (score >= 60) {
                performanceElement.textContent = '📚 還需努力，建議多加練習！';
            } else {
                performanceElement.textContent = '💪 加油！建議重新學習相關內容！';
            }
            
            // 顯示錯誤題目回顧
            reviewContainer.innerHTML = '';
            if (incorrectQuestions.length === 0) {
                reviewContainer.innerHTML = '<div style="text-align: center; color: #28a745; font-size: 1.2rem; padding: 20px;">🎊 恭喜！全部答對！</div>';
            } else {
                incorrectQuestions.forEach(item => {
                    const reviewCard = document.createElement('div');
                    reviewCard.className = 'question-card';
                    reviewCard.style.marginBottom = '20px';
                    
                    reviewCard.innerHTML = `
                        <div style="color: #dc3545; font-weight: bold; margin-bottom: 15px;">
                            ❌ 第 ${item.questionIndex + 1} 題 (答錯)
                        </div>
                        <div class="question-text">${item.question.q}</div>
                        <div class="options">
                            ${item.question.options.map((option, index) => {
                                let className = 'option';
                                if (index === item.correctAnswer) {
                                    className += ' correct';
                                } else if (index === item.userAnswer) {
                                    className += ' incorrect';
                                }
                                return `
                                    <div class="${className}">
                                        <div class="option-letter">${String.fromCharCode(65 + index)}</div>
                                        <div>${option}</div>
                                        ${index === item.correctAnswer ? ' ✓' : ''}
                                        ${index === item.userAnswer && index !== item.correctAnswer ? ' ✗' : ''}
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `;
                    
                    reviewContainer.appendChild(reviewCard);
                });
            }
            
            showPage('resultPage');
        }
        
        // 頁面導航
        function showPage(pageId) {
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));
            document.getElementById(pageId).classList.add('active');
        }
        
        function goToHome() {
            showPage('homePage');
        }
        
        function goToChapters() {
            showPage('chaptersPage');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 為輸入框添加 Enter 鍵監聽
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html>