# Firebase 設定說明

## 1. Firebase 專案配置

請在 `src/firebase/config.js` 中更新你的 Firebase 配置：

```javascript
const firebaseConfig = {
  apiKey: "你的-api-key",
  authDomain: "exam-proj-aea00.firebaseapp.com",
  databaseURL: "https://exam-proj-aea00-default-rtdb.firebaseio.com",
  projectId: "exam-proj-aea00",
  storageBucket: "exam-proj-aea00.appspot.com",
  messagingSenderId: "你的-sender-id",
  appId: "你的-app-id"
}
```

## 2. Firebase Authentication 設定

1. 在 Firebase Console 中啟用 Authentication
2. 啟用 Email/Password 登入方式

## 3. Realtime Database 設定

### 資料庫結構

```json
{
  "中文": {
    "第五章": [
      {
        "題目": "下列哪些屬於即時通訊？",
        "是否複選": true,
        "選項": [
          { "代號": "A", "內容": "MSN" },
          { "代號": "B", "內容": "Whatsapp" },
          { "代號": "C", "內容": "WeChat" },
          { "代號": "D", "內容": "M+" }
        ],
        "正確答案": ["A", "B", "C", "D"]
      }
    ]
  },
  "userAnswers": {
    "用戶UID": {
      "答題記錄ID": {
        "language": "中文",
        "chapter": {...},
        "score": 85,
        "timestamp": 1234567890,
        "userAnswers": [...],
        "questions": [...]
      }
    }
  }
}
```

### 安全規則

```json
{
  "rules": {
    "中文": {
      ".read": "auth != null"
    },
    "English": {
      ".read": "auth != null"
    },
    "userAnswers": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    }
  }
}
```

## 4. 功能說明

### 已實現功能：
- ✅ Firebase 電子郵件+密碼認證
- ✅ 用戶註冊和登入
- ✅ 從 Realtime Database 讀取題目
- ✅ 答題記錄儲存到 Realtime Database
- ✅ 路由保護（需要登入才能訪問）
- ✅ 認證狀態監聽

### 使用方式：
1. 用戶註冊/登入
2. 選擇語言（中文/English）
3. 選擇章節（從 Firebase 動態載入）
4. 開始考試（題目從 Firebase 載入）
5. 完成考試後自動儲存記錄到 Firebase
6. 查看考試歷史（從 Firebase 載入）
