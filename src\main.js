import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useExamStore } from './stores/examStore'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 初始化 Firebase 認證狀態監聽和 Cookie 檢查
const examStore = useExamStore()

// 先檢查 Cookie 中的登入狀態
examStore.checkLoginFromCookie()

// 然後初始化 Firebase 認證監聽
examStore.initAuth()

app.mount('#app')
