<template>
  <div>
    <!-- 頂部導航欄 -->
    <TopNavigation />

    <div class="exam-system-container">
      <div class="exam-card large">
        <div class="card text-white mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
          <div class="card-body text-center p-4">
            <div class="display-1 fw-bold mb-2">{{ score }}</div>
            <h4 class="mb-3">您的成績</h4>
            <p class="fs-5 mb-4">{{ performanceMessage }}</p>
            <div class="row g-3">
              <div class="col-6">
                <div class="d-flex align-items-center justify-content-center gap-2">
                  <i class="fas fa-check-circle"></i>
                  <span>答對：{{ correctCount }} 題</span>
                </div>
              </div>
              <div class="col-6">
                <div class="d-flex align-items-center justify-content-center gap-2">
                  <i class="fas fa-times-circle"></i>
                  <span>答錯：{{ incorrectQuestions.length }} 題</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h3 class="text-center mb-4 d-flex align-items-center justify-content-center gap-2">
            <i class="fas fa-search"></i>
            答題回顧
          </h3>

          <div v-if="incorrectQuestions.length === 0" class="alert alert-success text-center p-4">
            <i class="fas fa-trophy fs-1 mb-3 d-block"></i>
            <h4 class="fw-bold mb-2">恭喜！全部答對！</h4>
            <p class="mb-0">您的表現非常出色！</p>
          </div>

          <div v-else class="review-container">
            <div v-for="item in incorrectQuestions" :key="item.questionIndex" class="review-question">
              <div class="review-header">
                <i class="fas fa-times-circle"></i>
                第 {{ item.questionIndex + 1 }} 題 (答錯)
              </div>

              <div class="review-question-text">{{ item.question.q }}</div>

              <div class="review-options">
                <div v-for="(option, index) in item.question.options" :key="index" class="review-option" :class="{
                  correct: index === item.correctAnswer,
                  incorrect: index === item.userAnswer && index !== item.correctAnswer
                }">
                  <div class="option-letter">{{ String.fromCharCode(65 + index) }}</div>
                  <div class="option-text">{{ option }}</div>
                  <div class="option-indicator">
                    <i v-if="index === item.correctAnswer" class="fas fa-check"></i>
                    <i v-else-if="index === item.userAnswer" class="fas fa-times"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex gap-3 justify-content-center mb-4 flex-wrap">
          <button class="btn btn-secondary d-flex align-items-center gap-2" @click="retakeExam">
            <i class="fas fa-redo"></i>
            重新測驗
          </button>
          <button class="btn btn-gradient d-flex align-items-center gap-2" @click="goToChapters">
            <i class="fas fa-list"></i>
            返回章節選擇
          </button>
        </div>

        <div class="row g-2 text-center">
          <div class="col-md-4">
            <div class="alert alert-info mb-0 py-2">
              <i class="fas fa-book me-1"></i>
              章節：{{ currentChapter?.title }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-info mb-0 py-2">
              <i class="fas fa-language me-1"></i>
              語言：{{ currentLanguageText }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-info mb-0 py-2">
              <i class="fas fa-clock me-1"></i>
              完成時間：{{ currentTime }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import TopNavigation from '../components/TopNavigation.vue'

const router = useRouter()
const examStore = useExamStore()
const currentTime = ref('')

// 檢查考試狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
} else if (examStore.score === 0 && examStore.questions.length === 0) {
  router.push('/chapters')
}

const score = computed(() => examStore.score)
const correctCount = computed(() => examStore.correctCount)
const incorrectQuestions = computed(() => examStore.incorrectQuestions)
const performanceMessage = computed(() => examStore.getPerformanceMessage)
const currentChapter = computed(() => examStore.currentChapter)

const currentLanguageText = computed(() => {
  return examStore.currentLanguage === 'chinese' ? '中文' : 'English'
})

const retakeExam = async () => {
  if (currentChapter.value) {
    const success = await examStore.startExam(currentChapter.value)
    if (success) {
      router.push(`/exam/${currentChapter.value.id}`)
    }
  }
}

const goToChapters = () => {
  examStore.resetExam()
  examStore.selectLanguage(examStore.currentLanguage)
  router.push('/chapters')
}

onMounted(() => {
  currentTime.value = new Date().toLocaleString('zh-TW')
})
</script>

<style scoped>
@media (max-width: 768px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-1 {
    font-size: 3rem !important;
  }

  .d-flex.gap-3 {
    flex-direction: column;
  }
}
</style>
