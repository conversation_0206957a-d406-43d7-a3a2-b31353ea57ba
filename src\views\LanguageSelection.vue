<template>
  <div>
    <!-- 頂部導航欄 -->
    <TopNavigation />

    <div class="exam-system-container">
      <div class="exam-card">
        <div class="text-center mb-4">
          <h1 class="display-4 gradient-text fw-bold mb-3">選擇測驗語言</h1>
          <p class="text-muted fs-5">請選擇您要練習的語言</p>
        </div>

        <div class="row g-3 mb-4">
          <div class="col-md-6">
            <button class="language-card chinese w-100 h-100 border-0" @click="selectLanguage('chinese')">
              <div style="font-size: 3rem;">🇹🇼</div>
              <div class="fs-4 fw-bold">中文測驗</div>
              <div class="opacity-75">Traditional Chinese</div>
            </button>
          </div>

          <div class="col-md-6">
            <button class="language-card english w-100 h-100 border-0" @click="selectLanguage('english')">
              <div style="font-size: 3rem;">🇺🇸</div>
              <div class="fs-4 fw-bold">English Test</div>
              <div class="opacity-75">英文測驗</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import TopNavigation from '../components/TopNavigation.vue'

const router = useRouter()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const selectLanguage = (language) => {
  examStore.selectLanguage(language)
  router.push('/chapters')
}
</script>

<style scoped>
.exam-card {
  max-width: 600px;
}

@media (max-width: 600px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .language-card {
    padding: 30px 15px;
  }
}
</style>
