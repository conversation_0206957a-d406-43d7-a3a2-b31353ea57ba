<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
    <div class="container-fluid">
      <!-- Logo -->
      <router-link to="/language" class="navbar-brand fw-bold gradient-text">
        📚考試練習系統
      </router-link>

      <!-- Mobile toggle -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation items -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a href="#" class="nav-link" :class="{ active: examStore.currentLanguage === 'chinese' }"
              @click.prevent="selectChinese">
              <i class="fas fa-language me-1"></i>
              中文測驗
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link" :class="{ active: examStore.currentLanguage === 'english' }"
              @click.prevent="selectEnglish">
              <i class="fas fa-globe me-1"></i>
              英文測驗
            </a>
          </li>
          <li class="nav-item">
            <router-link to="/history" class="nav-link" :class="{ active: $route.path.startsWith('/history') }">
              <i class="fas fa-history me-1"></i>
              考試歷史
            </router-link>
          </li>
        </ul>

        <!-- User info -->
        <div class="navbar-nav" v-if="examStore.user.isLoggedIn">
          <div class="nav-item dropdown">
            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button"
              data-bs-toggle="dropdown">
              <i class="fas fa-user-circle me-2"></i>
              {{ getUserDisplayName }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <span class="dropdown-item-text">
                  <i class="fas fa-info-circle me-2"></i>
                  登入者：{{ getUserDisplayName }}
                </span>
              </li>
              <li>
                <hr class="dropdown-divider">
              </li>
              <li>
                <button class="dropdown-item text-danger" @click="handleLogout">
                  <i class="fas fa-sign-out-alt me-2"></i>
                  登出
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'

const router = useRouter()
const examStore = useExamStore()

// 獲取用戶顯示名稱（電郵@前的部分）
const getUserDisplayName = computed(() => {
  console.log('TopNavigation - 用戶資訊:', examStore.user)
  if (examStore.user.email) {
    const displayName = examStore.user.email.split('@')[0]
    console.log('TopNavigation - 顯示名稱:', displayName)
    return displayName
  }
  console.log('TopNavigation - 沒有電郵，顯示預設名稱')
  return '用戶'
})

const selectChinese = () => {
  examStore.selectLanguage('chinese')
  router.push('/chapters')
}

const selectEnglish = () => {
  examStore.selectLanguage('english')
  router.push('/chapters')
}

const handleLogout = async () => {
  await examStore.logout()
  router.push('/')
}
</script>

<style scoped>
.navbar-brand {
  font-size: 1.5rem;
}

.nav-link.active {
  color: #667eea !important;
  font-weight: 600;
}

.nav-link:hover {
  color: #667eea !important;
}

.dropdown-item-text {
  color: #6c757d;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .navbar-brand {
    font-size: 1.2rem;
  }
}
</style>
